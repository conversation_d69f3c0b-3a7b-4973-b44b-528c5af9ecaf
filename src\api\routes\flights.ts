import { Hono } from 'hono'
import { authMiddleware } from '../middleware/auth'
import {
  createFlightSchema,
  updateFlightSchema,
  validateExcelImport,
  validateExcelBatch
} from '../../lib/flight-validator'
import { updateFlightFieldSchema, validateFlightField } from '../../lib/flight-validation'
import { parseExcelFile } from '../../lib/excel-parser'
import {
  excelImportSchema,
  previewChangesResponseSchema,
  insertRowSchema,
  deleteRowSchema,
  type PreviewChangeItem,
  type PreviewFieldChange,
  type Flight,
  type InsertRowData,
  type DeleteRowData
} from '../../lib/flight-schemas'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply auth middleware to all routes
app.use('*', authMiddleware)

// GET /api/flights - L<PERSON>y danh sách lịch bay
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '2000') // Increase default limit to show all flights (up to 2000)
    const offset = (page - 1) * limit
    
    // Query parameters for filtering
    const date = c.req.query('date') // YYYY-MM-DD
    const search = c.req.query('search') // Search in flight numbers and staff
    const type = c.req.query('type') // 'arrival' or 'departure'
    
    let whereClause = ''
    const bindings: any[] = []
    
    // Build WHERE clause based on filters
    const conditions: string[] = []
    
    if (date) {
      conditions.push('date = ?')
      bindings.push(date)
    }
    
    if (search) {
      conditions.push(`(
        arr_flt LIKE ? OR dep_flt LIKE ? OR 
        arr_staff LIKE ? OR dep_staff LIKE ?
      )`)
      const searchPattern = `%${search}%`
      bindings.push(searchPattern, searchPattern, searchPattern, searchPattern)
    }
    
    if (type === 'arrival') {
      conditions.push('arr_flt IS NOT NULL')
    } else if (type === 'departure') {
      conditions.push('dep_flt IS NOT NULL')
    }
    
    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ')
    }
    
    // Get total count for pagination
    const countQuery = `SELECT COUNT(*) as total FROM flights ${whereClause}`
    const countResult = await c.env.DB.prepare(countQuery).bind(...bindings).first()
    const total = countResult?.total || 0
    
    // Get flights data
    const dataQuery = `
      SELECT * FROM flights 
      ${whereClause}
      ORDER BY date DESC, stt ASC
      LIMIT ? OFFSET ?
    `
    const { results } = await c.env.DB.prepare(dataQuery)
      .bind(...bindings, limit, offset)
      .all()

    return c.json({
      success: true,
      data: results,
      total,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching flights:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách lịch bay'
    }, 500)
  }
})

// GET /api/flights/:id - Lấy chi tiết một chuyến bay
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    const result = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: 'Không tìm thấy chuyến bay'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching flight:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin chuyến bay'
    }, 500)
  }
})

// POST /api/flights - Tạo chuyến bay mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const user = c.get('user')
    
    // Validate request body
    const validationResult = createFlightSchema.safeParse(body)
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }
    
    const data = validationResult.data
    
    // Check if STT already exists for the date
    const existingFlight = await c.env.DB.prepare(`
      SELECT id FROM flights WHERE date = ? AND stt = ?
    `).bind(data.date, data.stt).first()
    
    if (existingFlight) {
      return c.json({
        success: false,
        error: `STT ${data.stt} đã tồn tại cho ngày ${data.date}`
      }, 400)
    }
    
    // Generate ID
    const id = crypto.randomUUID()
    
    // Insert new flight
    const result = await c.env.DB.prepare(`
      INSERT INTO flights (
        id, date, stt,
        arr_flt, arr_from, arr_reg, arr_time, arr_staff,
        dep_flt, dep_to, dep_reg, dep_time, dep_staff,
        remark, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id, data.date, data.stt,
      data.arr_flt || null, data.arr_from || null, data.arr_reg || null,
      data.arr_time || null, data.arr_staff || null,
      data.dep_flt || null, data.dep_to || null, data.dep_reg || null,
      data.dep_time || null, data.dep_staff || null,
      data.remark || null, user?.username || 'system', user?.username || 'system'
    ).run()

    if (!result.success) {
      throw new Error('Failed to insert flight')
    }

    // Get the created flight
    const createdFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()

    return c.json({
      success: true,
      data: createdFlight,
      message: 'Tạo chuyến bay thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating flight:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo chuyến bay'
    }, 500)
  }
})

// PUT /api/flights/:id - Cập nhật chuyến bay
app.put('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const body = await c.req.json()
    const user = c.get('user')
    
    // Validate request body
    const validationResult = updateFlightSchema.safeParse(body)
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }
    
    const data = validationResult.data
    
    // Check if flight exists
    const existingFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()
    
    if (!existingFlight) {
      return c.json({
        success: false,
        error: 'Không tìm thấy chuyến bay'
      }, 404)
    }
    
    // Check STT conflict if date or stt is being updated
    if ((data.date && data.date !== existingFlight.date) || 
        (data.stt && data.stt !== existingFlight.stt)) {
      const checkDate = data.date || existingFlight.date
      const checkStt = data.stt || existingFlight.stt
      
      const conflictFlight = await c.env.DB.prepare(`
        SELECT id FROM flights WHERE date = ? AND stt = ? AND id != ?
      `).bind(checkDate, checkStt, id).first()
      
      if (conflictFlight) {
        return c.json({
          success: false,
          error: `STT ${checkStt} đã tồn tại cho ngày ${checkDate}`
        }, 400)
      }
    }
    
    // Build update query dynamically
    const updateFields: string[] = []
    const updateValues: any[] = []
    
    Object.entries(data).forEach(([key, value]) => {
      updateFields.push(`${key} = ?`)
      updateValues.push(value)
    })
    
    if (updateFields.length === 0) {
      return c.json({
        success: false,
        error: 'Không có dữ liệu để cập nhật'
      }, 400)
    }
    
    // Add updated_by and updated_at
    updateFields.push('updated_by = ?', 'updated_at = CURRENT_TIMESTAMP')
    updateValues.push(user?.username || 'system')
    
    // Add ID for WHERE clause
    updateValues.push(id)
    
    const updateQuery = `
      UPDATE flights 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `
    
    const result = await c.env.DB.prepare(updateQuery).bind(...updateValues).run()
    
    if (!result.success) {
      throw new Error('Failed to update flight')
    }
    
    // Get updated flight
    const updatedFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()

    return c.json({
      success: true,
      data: updatedFlight,
      message: 'Cập nhật chuyến bay thành công'
    })
  } catch (error) {
    console.error('Error updating flight:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật chuyến bay'
    }, 500)
  }
})

// DELETE /api/flights/:id - Xóa chuyến bay
app.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    // Check if flight exists
    const existingFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()
    
    if (!existingFlight) {
      return c.json({
        success: false,
        error: 'Không tìm thấy chuyến bay'
      }, 404)
    }
    
    // Delete flight (cascade will handle flight_changes)
    const result = await c.env.DB.prepare(`
      DELETE FROM flights WHERE id = ?
    `).bind(id).run()
    
    if (!result.success) {
      throw new Error('Failed to delete flight')
    }

    return c.json({
      success: true,
      message: 'Xóa chuyến bay thành công',
      data: existingFlight
    })
  } catch (error) {
    console.error('Error deleting flight:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa chuyến bay'
    }, 500)
  }
})

// PATCH /api/flights/:id - Cập nhật single field
app.patch('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const body = await c.req.json()
    const user = c.get('user')

    // Debug log
    console.log('PATCH /api/flights/:id - Request:', {
      id,
      body,
      user: user?.username
    })

    // Validate request body
    const validationResult = updateFlightFieldSchema.safeParse({
      flightId: id,
      field: body.field,
      value: body.value
    })

    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }

    const { field, value } = validationResult.data

    // Check if flight exists and get current value
    const existingFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()

    if (!existingFlight) {
      return c.json({
        success: false,
        error: 'Không tìm thấy chuyến bay'
      }, 404)
    }

    const oldValue = existingFlight[field] || null
    const newValue = value || null

    // Skip update if values are the same
    if (oldValue === newValue) {
      return c.json({
        success: true,
        data: existingFlight,
        message: 'Không có thay đổi'
      })
    }

    // Validate field value
    try {
      if (newValue !== null) {
        validateFlightField(field, newValue)
      }
    } catch (validationError) {
      return c.json({
        success: false,
        error: validationError instanceof Error ? validationError.message : 'Giá trị không hợp lệ'
      }, 400)
    }

    // Update the field
    const result = await c.env.DB.prepare(`
      UPDATE flights
      SET ${field} = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(newValue, user?.username || 'system', id).run()

    if (!result.success) {
      throw new Error('Failed to update flight field')
    }

    // Log the change
    const changeId = crypto.randomUUID()
    await c.env.DB.prepare(`
      INSERT INTO flight_changes (
        id, flight_id, field_name, old_value, new_value, changed_by
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      changeId,
      id,
      field,
      oldValue,
      newValue,
      user?.username || 'system'
    ).run()

    // Get updated flight
    const updatedFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(id).first()

    return c.json({
      success: true,
      data: updatedFlight,
      change: {
        id: changeId,
        field,
        oldValue,
        newValue,
        changedBy: user?.username || 'system',
        changedAt: new Date().toISOString()
      },
      message: `Cập nhật ${field} thành công`
    })
  } catch (error) {
    console.error('Error updating flight field:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      flightId: c.req.param('id'),
      requestBody: body
    })
    return c.json({
      success: false,
      error: 'Không thể cập nhật trường dữ liệu',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// POST /api/flights/batch-update - Batch update multiple flights
app.post('/batch-update', async (c) => {
  try {
    const body = await c.req.json()
    const user = c.get('user')

    if (!body.updates || !Array.isArray(body.updates)) {
      return c.json({
        success: false,
        error: 'Dữ liệu cập nhật không hợp lệ'
      }, 400)
    }

    const updates = body.updates
    let updatedCount = 0
    const errors: string[] = []

    // Process each update
    for (const update of updates) {
      try {
        const { flightId, field, value } = update

        // Validate field value
        if (value !== null && value !== undefined) {
          validateFlightField(field, value)
        }

        // Get current value for change tracking
        const existingFlight = await c.env.DB.prepare(`
          SELECT ${field} FROM flights WHERE id = ?
        `).bind(flightId).first()

        if (!existingFlight) {
          errors.push(`Không tìm thấy chuyến bay ${flightId}`)
          continue
        }

        const oldValue = existingFlight[field] || null
        const newValue = value || null

        // Skip if no change
        if (oldValue === newValue) {
          continue
        }

        // Update the field
        const result = await c.env.DB.prepare(`
          UPDATE flights
          SET ${field} = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(newValue, user?.username || 'system', flightId).run()

        if (result.success) {
          updatedCount++

          // Log the change
          const changeId = crypto.randomUUID()
          await c.env.DB.prepare(`
            INSERT INTO flight_changes (
              id, flight_id, field_name, old_value, new_value, changed_by
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).bind(
            changeId,
            flightId,
            field,
            oldValue,
            newValue,
            user?.username || 'system'
          ).run()
        } else {
          errors.push(`Không thể cập nhật chuyến bay ${flightId}`)
        }
      } catch (error) {
        errors.push(`Lỗi cập nhật ${update.flightId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return c.json({
      success: true,
      data: {
        updatedCount,
        totalRequested: updates.length,
        errors
      },
      message: `Cập nhật thành công ${updatedCount}/${updates.length} chuyến bay`
    })
  } catch (error) {
    console.error('Error in batch update:', error)
    return c.json({
      success: false,
      error: 'Không thể thực hiện cập nhật hàng loạt'
    }, 500)
  }
})

// POST /api/flights/import - Import flights from Excel
app.post('/import', async (c) => {
  try {
    const user = c.get('user')
    const body = await c.req.parseBody()

    // Extract form data
    const file = body['file'] as File
    const date = body['date'] as string
    const mode = (body['mode'] as string) || 'replace'
    const sheetName = body['sheetName'] as string
    const skipRows = parseInt((body['skipRows'] as string) || '0')
    const validateOnly = (body['validateOnly'] as string) === 'true'

    // Validate required fields
    if (!file) {
      return c.json({
        success: false,
        error: 'File Excel là bắt buộc'
      }, 400)
    }

    if (!date) {
      return c.json({
        success: false,
        error: 'Ngày lịch bay là bắt buộc'
      }, 400)
    }

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return c.json({
        success: false,
        error: 'Ngày phải có định dạng YYYY-MM-DD'
      }, 400)
    }

    const startTime = Date.now()
    const importId = crypto.randomUUID()

    try {
      // Parse Excel file
      const parseResult = await parseExcelFile(file, {
        sheetName,
        skipRows,
        validateData: true
      })

      if (!parseResult.success || !parseResult.data) {
        return c.json({
          success: false,
          error: 'Không thể phân tích file Excel',
          details: parseResult.errors,
          importId,
          filename: file.name,
          totalRows: parseResult.metadata.totalRows,
          processedRows: 0,
          successRows: 0,
          errorRows: parseResult.errors?.length || 0,
          processingTimeMs: Date.now() - startTime
        }, 400)
      }

      // Validate business rules
      const validationResult = validateExcelBatch(parseResult.data, date)
      const validFlights = validationResult.results
        .filter(r => r.success && r.data)
        .map(r => r.data!)

      const errors = validationResult.results
        .filter(r => !r.success)
        .flatMap(r => r.errors || [])

      // If validation only, return results without importing
      if (validateOnly) {
        return c.json({
          success: validFlights.length > 0,
          importId,
          filename: file.name,
          totalRows: parseResult.data.length,
          processedRows: parseResult.data.length,
          successRows: validFlights.length,
          errorRows: errors.length,
          errors: errors.map(err => ({
            row: err.row || 0,
            field: err.field,
            message: err.message
          })),
          summary: `Kiểm tra hoàn tất: ${validFlights.length}/${parseResult.data.length} dòng hợp lệ`,
          processingTimeMs: Date.now() - startTime
        })
      }

      if (validFlights.length === 0) {
        return c.json({
          success: false,
          error: 'Không có dữ liệu hợp lệ để import',
          details: errors,
          importId,
          filename: file.name,
          totalRows: parseResult.data.length,
          processedRows: parseResult.data.length,
          successRows: 0,
          errorRows: errors.length,
          processingTimeMs: Date.now() - startTime
        }, 400)
      }

      // Start database transaction
      let successCount = 0
      let errorCount = 0
      const importErrors: any[] = []

      // Handle different import modes
      if (mode === 'replace') {
        // Delete existing flights for the date
        await c.env.DB.prepare(`DELETE FROM flights WHERE date = ?`).bind(date).run()
      }

      // Insert valid flights
      for (const flight of validFlights) {
        try {
          const flightId = crypto.randomUUID()

          if (mode === 'update') {
            // Try to update existing flight first
            const existingFlight = await c.env.DB.prepare(`
              SELECT id FROM flights WHERE date = ? AND stt = ?
            `).bind(date, flight.stt).first()

            if (existingFlight) {
              // Update existing flight
              await c.env.DB.prepare(`
                UPDATE flights SET
                  arr_flt = ?, arr_from = ?, arr_reg = ?, arr_time = ?, arr_staff = ?,
                  dep_flt = ?, dep_to = ?, dep_reg = ?, dep_time = ?, dep_staff = ?,
                  remark = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
              `).bind(
                flight.arr_flt || null, flight.arr_from || null, flight.arr_reg || null,
                flight.arr_time || null, flight.arr_staff || null,
                flight.dep_flt || null, flight.dep_to || null, flight.dep_reg || null,
                flight.dep_time || null, flight.dep_staff || null,
                flight.remark || null, user?.username || 'system',
                existingFlight.id
              ).run()

              successCount++
              continue
            }
          }

          // Check for STT conflict in append mode
          if (mode === 'append') {
            const existingFlight = await c.env.DB.prepare(`
              SELECT id FROM flights WHERE date = ? AND stt = ?
            `).bind(date, flight.stt).first()

            if (existingFlight) {
              errorCount++
              importErrors.push({
                row: flight.stt,
                field: 'stt',
                message: `STT ${flight.stt} đã tồn tại cho ngày ${date}`
              })
              continue
            }
          }

          // Insert new flight
          const result = await c.env.DB.prepare(`
            INSERT INTO flights (
              id, date, stt,
              arr_flt, arr_from, arr_reg, arr_time, arr_staff,
              dep_flt, dep_to, dep_reg, dep_time, dep_staff,
              remark, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            flightId, date, flight.stt,
            flight.arr_flt || null, flight.arr_from || null, flight.arr_reg || null,
            flight.arr_time || null, flight.arr_staff || null,
            flight.dep_flt || null, flight.dep_to || null, flight.dep_reg || null,
            flight.dep_time || null, flight.dep_staff || null,
            flight.remark || null, user?.username || 'system', user?.username || 'system'
          ).run()

          if (result.success) {
            successCount++
          } else {
            errorCount++
            importErrors.push({
              row: flight.stt,
              message: 'Lỗi khi lưu vào database'
            })
          }
        } catch (error) {
          errorCount++
          importErrors.push({
            row: flight.stt,
            message: error instanceof Error ? error.message : 'Lỗi không xác định'
          })
        }
      }

      // Record import history
      try {
        await c.env.DB.prepare(`
          INSERT INTO flight_imports (
            id, filename, imported_by, total_rows, success_rows, error_rows, errors
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          importId,
          file.name,
          user?.username || 'system',
          parseResult.data.length,
          successCount,
          errorCount + errors.length,
          JSON.stringify([...errors, ...importErrors])
        ).run()
      } catch (error) {
        console.error('Failed to record import history:', error)
      }

      const processingTime = Date.now() - startTime

      return c.json({
        success: successCount > 0,
        importId,
        filename: file.name,
        totalRows: parseResult.data.length,
        processedRows: validFlights.length,
        successRows: successCount,
        errorRows: errorCount + errors.length,
        errors: [...errors, ...importErrors].map(err => ({
          row: err.row || 0,
          field: err.field,
          message: err.message
        })),
        summary: `Import hoàn tất: ${successCount}/${validFlights.length} dòng thành công`,
        processingTimeMs: processingTime
      }, successCount > 0 ? 200 : 400)

    } catch (parseError) {
      return c.json({
        success: false,
        error: 'Lỗi khi xử lý file Excel',
        details: parseError instanceof Error ? parseError.message : 'Lỗi không xác định',
        importId,
        filename: file.name,
        totalRows: 0,
        processedRows: 0,
        successRows: 0,
        errorRows: 1,
        processingTimeMs: Date.now() - startTime
      }, 500)
    }

  } catch (error) {
    console.error('Error importing flights:', error)
    return c.json({
      success: false,
      error: 'Không thể import lịch bay từ Excel'
    }, 500)
  }
})

// GET /api/flights/changes - Lấy lịch sử thay đổi
app.get('/changes', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '2000') // Increase default limit for consistency
    const offset = (page - 1) * limit
    const flightId = c.req.query('flightId')

    let whereClause = ''
    const bindings: any[] = []

    if (flightId) {
      whereClause = 'WHERE fc.flight_id = ?'
      bindings.push(flightId)
    }

    // Get changes with flight info and user full name
    const changes = await c.env.DB.prepare(`
      SELECT
        fc.*,
        f.date,
        f.stt,
        f.arr_flt,
        f.dep_flt,
        COALESCE(u.full_name, fc.changed_by) as changer_name
      FROM flight_changes fc
      LEFT JOIN flights f ON fc.flight_id = f.id
      LEFT JOIN users u ON fc.changed_by = u.username
      ${whereClause}
      ORDER BY fc.changed_at DESC
      LIMIT ? OFFSET ?
    `).bind(...bindings, limit, offset).all()

    // Get total count
    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total
      FROM flight_changes fc
      ${whereClause}
    `).bind(...bindings).first()

    const total = countResult?.total || 0
    const totalPages = Math.ceil(total / limit)

    return c.json({
      success: true,
      data: changes.results || [],
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching flight changes:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử thay đổi'
    }, 500)
  }
})

// GET /api/flights/:id/changes - Lấy lịch sử thay đổi của 1 chuyến bay
app.get('/:id/changes', async (c) => {
  try {
    const flightId = c.req.param('id')
    const fieldName = c.req.query('field') // Optional field filter

    let query = `
      SELECT
        fc.*,
        COALESCE(u.full_name, fc.changed_by) as changer_name
      FROM flight_changes fc
      LEFT JOIN users u ON fc.changed_by = u.username
      WHERE fc.flight_id = ?
    `
    const bindings = [flightId]

    // Add field filter if specified
    if (fieldName) {
      query += ` AND fc.field_name = ?`
      bindings.push(fieldName)
    }

    query += ` ORDER BY fc.changed_at DESC`

    const changes = await c.env.DB.prepare(query).bind(...bindings).all()

    return c.json({
      success: true,
      data: changes.results || []
    })
  } catch (error) {
    console.error('Error fetching flight changes:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử thay đổi'
    }, 500)
  }
})

// POST /api/flights/preview-changes - Preview changes for Update mode import
app.post('/preview-changes', async (c) => {
  try {
    const user = c.get('user')
    const body = await c.req.parseBody()

    // Extract form data
    const file = body['file'] as File
    const date = body['date'] as string
    const mode = (body['mode'] as string) || 'update'
    const sheetName = body['sheetName'] as string
    const skipRows = parseInt((body['skipRows'] as string) || '0')

    // Validate required fields
    if (!file) {
      return c.json({
        success: false,
        error: 'File Excel là bắt buộc'
      }, 400)
    }

    if (!date) {
      return c.json({
        success: false,
        error: 'Ngày lịch bay là bắt buộc'
      }, 400)
    }

    if (mode !== 'update') {
      return c.json({
        success: false,
        error: 'Preview changes chỉ hỗ trợ chế độ update'
      }, 400)
    }

    // Parse Excel file
    const parseResult = await parseExcelFile(file, {
      sheetName,
      skipRows,
      validateData: true
    })

    if (!parseResult.success || !parseResult.data) {
      return c.json({
        success: false,
        error: 'Không thể parse file Excel',
        details: parseResult.errors
      }, 400)
    }

    // Get existing flights for the date
    const existingFlights = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE date = ? ORDER BY stt
    `).bind(date).all()

    const existingFlightsMap = new Map<number, Flight>()
    if (existingFlights.results) {
      for (const flight of existingFlights.results as Flight[]) {
        existingFlightsMap.set(flight.stt, flight)
      }
    }

    // Generate preview changes
    const changes: PreviewChangeItem[] = []
    let updateCount = 0
    let insertCount = 0
    let changedFieldsCount = 0

    // Validate parseResult.data
    const flightDataArray = Array.isArray(parseResult.data) ? parseResult.data : []

    for (const newFlightData of flightDataArray) {
      const existingFlight = existingFlightsMap.get(newFlightData.stt)

      if (existingFlight) {
        // This is an update
        const fieldChanges = generateFieldChanges(existingFlight, newFlightData)
        const hasChanges = fieldChanges.some(change => change.hasChanged)

        if (hasChanges) {
          changedFieldsCount += fieldChanges.filter(change => change.hasChanged).length
        }

        changes.push({
          type: 'update',
          stt: newFlightData.stt,
          flightId: existingFlight.id,
          oldData: existingFlight,
          newData: { ...newFlightData, date },
          fieldChanges,
          hasChanges
        })

        updateCount++
      } else {
        // This is an insert
        const fieldChanges = generateFieldChanges(null, newFlightData)

        changes.push({
          type: 'insert',
          stt: newFlightData.stt,
          newData: { ...newFlightData, date },
          fieldChanges,
          hasChanges: true
        })

        insertCount++
        changedFieldsCount += fieldChanges.length
      }
    }

    return c.json({
      success: true,
      changes,
      summary: {
        totalItems: changes.length,
        updateCount,
        insertCount,
        changedFieldsCount
      },
      date,
      filename: file.name
    })

  } catch (error) {
    console.error('Error previewing changes:', error)
    return c.json({
      success: false,
      error: 'Không thể preview changes'
    }, 500)
  }
})

// Helper function to generate field changes comparison
function generateFieldChanges(oldFlight: Flight | null, newFlight: any): PreviewFieldChange[] {
  const fieldDisplayNames: Record<string, string> = {
    'arr_flt': 'FLT (Đến)',
    'arr_from': 'FROM',
    'arr_reg': 'REG (Đến)',
    'arr_time': 'STA',
    'arr_staff': 'STAFF (Đến)',
    'dep_flt': 'FLT (Đi)',
    'dep_to': 'TO',
    'dep_reg': 'REG (Đi)',
    'dep_time': 'STD',
    'dep_staff': 'STAFF (Đi)',
    'remark': 'REMARK'
  }

  const fields = [
    'arr_flt', 'arr_from', 'arr_reg', 'arr_time', 'arr_staff',
    'dep_flt', 'dep_to', 'dep_reg', 'dep_time', 'dep_staff',
    'remark'
  ]

  return fields.map(field => {
    const oldValue = oldFlight ? (oldFlight[field as keyof Flight] as string) || null : null
    const newValue = newFlight[field] || null

    return {
      field,
      fieldDisplayName: fieldDisplayNames[field] || field,
      oldValue,
      newValue,
      hasChanged: oldValue !== newValue
    }
  })
}

// ============================================================================
// ROW OPERATIONS ENDPOINTS
// ============================================================================

// Helper function to reorder STT after insert/delete operations
async function reorderSTT(
  db: D1Database,
  date: string,
  startSTT: number,
  increment: number
): Promise<void> {
  console.log(`Reordering STT: date=${date}, startSTT=${startSTT}, increment=${increment}`)

  // Update all flights with STT >= startSTT
  const result = await db.prepare(`
    UPDATE flights
    SET stt = stt + ?, updated_at = CURRENT_TIMESTAMP
    WHERE date = ? AND stt >= ?
  `).bind(increment, date, startSTT).run()

  if (!result.success) {
    console.error('Failed to reorder STT:', {
      error: result.error,
      meta: result.meta,
      date,
      startSTT,
      increment
    })
    throw new Error(`Failed to reorder STT: ${result.error || 'Unknown database error'}`)
  }

  console.log(`Successfully reordered ${result.meta?.changes || 0} flights`)
}

// POST /api/flights/:flightId/insert-row - Insert row for arrival/departure
app.post('/:flightId/insert-row', async (c) => {
  try {
    const flightId = c.req.param('flightId')
    const body = await c.req.json()
    const user = c.get('user')

    console.log('Insert row request:', { flightId, body, user: user?.username })

    // Validate request body
    const validationResult = insertRowSchema.safeParse(body)
    if (!validationResult.success) {
      console.error('Validation failed:', validationResult.error.errors)
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }

    const { type, position, date } = validationResult.data
    console.log('Validated data:', { type, position, date })

    // Get the current flight
    const currentFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(flightId).first()

    if (!currentFlight) {
      return c.json({
        success: false,
        error: 'Chuyến bay không tồn tại'
      }, 404)
    }

    const flight = currentFlight as Flight
    let newSTT: number

    console.log('Starting row insertion operation')

    // Determine new STT and reorder strategy
    if (position === 'before') {
      newSTT = flight.stt
      console.log(`Inserting before STT ${flight.stt}, new STT will be ${newSTT}`)
    } else {
      newSTT = flight.stt + 1
      console.log(`Inserting after STT ${flight.stt}, new STT will be ${newSTT}`)
    }

    // First, reorder existing flights to make space
    if (position === 'before') {
      // Increment STT for flights >= current STT
      await reorderSTT(c.env.DB, date, flight.stt, 1)
    } else {
      // Increment STT for flights > current STT
      await reorderSTT(c.env.DB, date, flight.stt + 1, 1)
    }

    // Create new flight with only the specified type data
    // Generate ID in the same format as database default: lower(hex(randomblob(16)))
    const randomBytes = crypto.getRandomValues(new Uint8Array(16))
    const newFlightId = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('')

    let insertData: any = {
      id: newFlightId,
      date,
      stt: newSTT,
      created_by: user?.username || 'system',
      updated_by: user?.username || 'system'
    }

    if (type === 'arrival') {
      // Copy arrival data from current flight if exists
      insertData = {
        ...insertData,
        arr_flt: flight.arr_flt,
        arr_from: flight.arr_from,
        arr_reg: flight.arr_reg,
        arr_time: flight.arr_time,
        arr_staff: flight.arr_staff
      }
    } else {
      // Copy departure data from current flight if exists
      insertData = {
        ...insertData,
        dep_flt: flight.dep_flt,
        dep_to: flight.dep_to,
        dep_reg: flight.dep_reg,
        dep_time: flight.dep_time,
        dep_staff: flight.dep_staff
      }
    }

    // Insert new flight with all required columns in correct order
    const result = await c.env.DB.prepare(`
      INSERT INTO flights (
        id, date, stt,
        arr_flt, arr_from, arr_reg, arr_time, arr_staff,
        dep_flt, dep_to, dep_reg, dep_time, dep_staff,
        created_by, updated_by, remark,
        arr_present, arr_finished, dep_present, dep_boarded, dep_finished
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      insertData.id, insertData.date, insertData.stt,
      insertData.arr_flt || null, insertData.arr_from || null, insertData.arr_reg || null,
      insertData.arr_time || null, insertData.arr_staff || null,
      insertData.dep_flt || null, insertData.dep_to || null, insertData.dep_reg || null,
      insertData.dep_time || null, insertData.dep_staff || null,
      // created_at and updated_at will use CURRENT_TIMESTAMP default automatically
      insertData.created_by, insertData.updated_by, insertData.remark || null,
      // Status columns with default FALSE values
      false, false, false, false, false
    ).run()

    if (!result.success) {
      console.error('Database insert failed:', {
        error: result.error,
        meta: result.meta,
        insertData: {
          id: insertData.id,
          date: insertData.date,
          stt: insertData.stt,
          type,
          position
        }
      })

      // Rollback STT reordering if insert failed
      try {
        console.log('Rolling back STT reordering due to insert failure')
        if (position === 'before') {
          await reorderSTT(c.env.DB, date, flight.stt, -1)
        } else {
          await reorderSTT(c.env.DB, date, flight.stt + 1, -1)
        }
      } catch (rollbackError) {
        console.error('Failed to rollback STT reordering:', rollbackError)
      }

      throw new Error(`Failed to insert new flight row: ${result.error || 'Unknown database error'}`)
    }

    console.log('Successfully inserted new flight row:', {
      id: newFlightId,
      type,
      position,
      newSTT,
      originalSTT: flight.stt
    })

    return c.json({
      success: true,
      data: {
        id: newFlightId,
        type,
        position,
        newSTT,
        originalSTT: flight.stt
      }
    })

  } catch (error) {
    console.error('Error inserting flight row:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      flightId,
      body,
      user: user?.username
    })

    return c.json({
      success: false,
      error: 'Không thể chèn hàng mới',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// POST /api/flights/:flightId/delete-row - Delete row for arrival/departure (using POST to support body data)
app.post('/:flightId/delete-row', async (c) => {
  try {
    const flightId = c.req.param('flightId')
    const body = await c.req.json()

    // Validate request body
    const validationResult = deleteRowSchema.safeParse(body)
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }

    const { type, date } = validationResult.data

    // Get the current flight
    const currentFlight = await c.env.DB.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(flightId).first()

    if (!currentFlight) {
      return c.json({
        success: false,
        error: 'Chuyến bay không tồn tại'
      }, 404)
    }

    const flight = currentFlight as Flight

    if (type === 'arrival') {
      // Clear arrival data
      const result = await c.env.DB.prepare(`
        UPDATE flights
        SET arr_flt = NULL, arr_from = NULL, arr_reg = NULL,
            arr_time = NULL, arr_staff = NULL,
            arr_present = NULL, arr_finished = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(flightId).run()

      if (!result.success) {
        throw new Error('Failed to clear arrival data')
      }

      // Check if departure data exists, if not delete the entire row
      if (!flight.dep_flt && !flight.dep_to && !flight.dep_time) {
        await c.env.DB.prepare(`DELETE FROM flights WHERE id = ?`).bind(flightId).run()
        // Reorder STT: decrement for flights > current STT
        await reorderSTT(c.env.DB, date, flight.stt + 1, -1)
      }
    } else {
      // Clear departure data
      const result = await c.env.DB.prepare(`
        UPDATE flights
        SET dep_flt = NULL, dep_to = NULL, dep_reg = NULL,
            dep_time = NULL, dep_staff = NULL,
            dep_present = NULL, dep_boarded = NULL, dep_finished = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(flightId).run()

      if (!result.success) {
        throw new Error('Failed to clear departure data')
      }

      // Check if arrival data exists, if not delete the entire row
      if (!flight.arr_flt && !flight.arr_from && !flight.arr_time) {
        await c.env.DB.prepare(`DELETE FROM flights WHERE id = ?`).bind(flightId).run()
        // Reorder STT: decrement for flights > current STT
        await reorderSTT(c.env.DB, date, flight.stt + 1, -1)
      }
    }

    return c.json({
      success: true,
      data: {
        id: flightId,
        type,
        stt: flight.stt
      }
    })

  } catch (error) {
    console.error('Error deleting flight row:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa hàng'
    }, 500)
  }
})

export default app
